import pandas as pd

def combine_and_sum_daily_prices(file1_path, file2_path, date_column='A', price_column='B'):
    """
    Combines daily prices from two Excel files and sums them day by day.
    """
    try:
        # Load the dataframes
        df1 = pd.read_excel(file1_path)
        df2 = pd.read_excel(file2_path)
        
        # Debug: Print column names to see what's actually in the files
        print(f"Columns in {file1_path}: {list(df1.columns)}")
        print(f"Columns in {file2_path}: {list(df2.columns)}")
        print(f"Looking for date column: '{date_column}'")
        print(f"Looking for price column: '{price_column}'")
        
        # Check if columns exist in both files
        if date_column not in df1.columns:
            print(f"Date column '{date_column}' not found in {file1_path}")
            return None
        if price_column not in df1.columns:
            print(f"Price column '{price_column}' not found in {file1_path}")
            return None
        if date_column not in df2.columns:
            print(f"Date column '{date_column}' not found in {file2_path}")
            return None
        if price_column not in df2.columns:
            print(f"Price column '{price_column}' not found in {file2_path}")
            return None

        # Rename columns for easier access, assuming 'A' and 'B' are column names from Excel.
        # If your Excel actually has headers, adjust these names.
        # Function to clean and parse date strings
        def parse_and_clean_date(date_range_str):
            # Split the string to get the first date part
            first_date_part = str(date_range_str).split(' - ')[0]
            # Remove any trailing timezone information like ' (CET)'
            cleaned_date_str = first_date_part.split(' (')[0]
            return pd.to_datetime(cleaned_date_str, format='%d/%m/%Y %H:%M:%S')

        # Apply the cleaning and parsing function
        df1['Date'] = df1[date_column].apply(parse_and_clean_date)
        df2['Date'] = df2[date_column].apply(parse_and_clean_date)

        # Set 'Date' as index for easier grouping
        df1.set_index('Date', inplace=True)
        df2.set_index('Date', inplace=True)

        # Resample to daily frequency and sum the prices for each day
        daily_prices1 = df1[price_column].resample('D').sum()
        daily_prices2 = df2[price_column].resample('D').sum()

        # Combine the daily prices
        combined_daily_prices = pd.DataFrame({
            'Price_File1': daily_prices1,
            'Price_File2': daily_prices2
        }).fillna(0) # Fill NaN with 0 if a date exists in one file but not the other

        # Sum the prices from both files for each day
        combined_daily_prices['Combined_Price'] = combined_daily_prices['Price_File1'] + combined_daily_prices['Price_File2']

        # Reset index to make 'Date' a regular column again
        combined_daily_prices.reset_index(inplace=True)

        print("Combined Daily Prices (first 5 rows):")
        print(combined_daily_prices.head()) # Display the first few rows
        print("\nSummary of Combined Data:")
        combined_daily_prices.info()

        return combined_daily_prices

    except FileNotFoundError as e:
        print(f"Error: One of the files was not found. {e}")
        return None
    except KeyError as e:
        print(f"Error: Missing expected column. Please check 'date_column' and 'price_column' arguments. {e}")
        return None
    except Exception as e:
        print(f"An unexpected error occurred: {e}")
        return None

# --- Main execution ---
if __name__ == "__main__":
    file1 = 'BE+2017-2020.xlsx'
    file2 = 'GB+2017-2020.xlsx'

    # Use a mapping approach for different column names
    be_date_col = 'MTU (CET/CEST)'
    be_price_col = 'Day-ahead Price (EUR/MWh)'
    
    gb_date_col = 'Time'
    gb_price_col = 'price'
    
    # You'll need to modify the function to handle different column names per file
    combined_data = combine_and_sum_daily_prices(file1, file2, date_column=be_date_col, price_column=be_price_col)

    if combined_data is not None:
        # You can now further process or save combined_data
        # For example, save to a new Excel file:
        combined_data.to_excel("combined_daily_prices.xlsx", index=False)
        # print("\nCombined data saved to 'combined_daily_prices.xlsx'")
        pass
